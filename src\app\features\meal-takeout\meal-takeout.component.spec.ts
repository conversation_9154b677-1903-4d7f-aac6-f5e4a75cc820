
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { MealTakeoutComponent } from './meal-takeout.component';
import { LLMGROQService } from '../../../core/services/llm-groq.service';
import { GeolocationService } from '../../../core/services/geolocation.service';
import { SharedDataService } from '../../../core/services/shared-data.service';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatListModule } from '@angular/material/list';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('MealTakeoutComponent', () => {

  let component: MealTakeoutComponent;
  let fixture: ComponentFixture<MealTakeoutComponent>;
  let llmServiceSpy: jasmine.SpyObj<LLMGROQService>;
  let geolocationServiceSpy: jasmine.SpyObj<GeolocationService>;
  let sharedDataService: SharedDataService;
  let router: Router;

  // Mock data
  const mockPosition = { coords: { latitude: 123, longitude: 456 } };
  const mockLocation = { lat: 123, lng: 456 };
  const mockPreferences = {
    numberOfPeople: 4,
    dietaryRestrictions: {
      glutenFree: false,
      dairyFree: false,
      vegetarian: false,
      peanutAllergy: false,
      other: false,
    },
    cuisine: 'Italian',
  };
  const mockTakeoutSuggestions = [
    {
      name: 'Restaurant A',
      cuisine: 'Italian',
      estimatedTime: '15-20 minutes',
      address: '123 Main St',
      phone: '555-1234',
      dietaryOptions: ['gluten-free', 'vegetarian'],
    },
    {
      name: 'Restaurant B',
      cuisine: 'Italian',
      estimatedTime: '20-25 minutes',
      address: '456 Elm St',
      phone: '555-5678',
      dietaryOptions: ['dairy-free'],
    },
  ];

  beforeEach(async () => {
    const llmServiceSpy = jasmine.createSpyObj('LLMGROQService', ['getTakeoutRecommendations']);
    const geolocationServiceSpy = jasmine.createSpyObj('GeolocationService', ['getCurrentPosition']);

    await TestBed.configureTestingModule({
      imports: [
        MealTakeoutComponent,
        MatCardModule,
        MatButtonModule,
        MatProgressSpinnerModule,
        MatListModule,
        NoopAnimationsModule,
      ],
      providers: [
        SharedDataService,
        { provide: LLMGROQService, useValue: llmServiceSpy },
        { provide: GeolocationService, useValue: geolocationServiceSpy },
        { provide: Router, useValue: { navigate: jasmine.createSpy('navigate') } },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MealTakeoutComponent);
    component = fixture.componentInstance;
    llmService = TestBed.inject(LLMGROQService);
    geolocationService = TestBed.inject(GeolocationService);
    sharedDataService = TestBed.inject(SharedDataService);
    router = TestBed.inject(Router);
    llmServiceSpy = TestBed.inject(LLMGROQService) as jasmine.SpyObj<LLMGROQService>;
    geolocationServiceSpy = TestBed.inject(GeolocationService) as jasmine.SpyObj<GeolocationService>;
  });

  // Component creation and initialization
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Geolocation handling
  it('should request geolocation on init', () => {
    spyOn(geolocationService, 'getCurrentPosition').and.returnValue(Promise.resolve(mockPosition));
    component.ngOnInit();
    expect(geolocationService.getCurrentPosition).toHaveBeenCalled();
  });

  it('should handle geolocation permission denied', () => {
    spyOn(geolocationService, 'getCurrentPosition').and.returnValue(Promise.reject(new Error('Permission denied')));
    component.ngOnInit();
    // Should show fallback UI or manual location entry
  });

  // LLM API integration
  it('should call LLM service with correct location and preferences', () => {
    component.getTakeoutSuggestions(mockLocation, mockPreferences);
    expect(llmService.getTakeoutRecommendations).toHaveBeenCalledWith(mockLocation, mockPreferences);
  });

  it('should display takeout suggestions when API succeeds', () => {
    llmServiceSpy.getTakeoutRecommendations.and.returnValue(of(mockTakeoutSuggestions));
    component.getTakeoutSuggestions(mockLocation, mockPreferences);
    fixture.detectChanges();
    
    expect(component.suggestions).toEqual(mockTakeoutSuggestions);
    const restaurantElements = fixture.nativeElement.querySelectorAll('.restaurant-card');
    expect(restaurantElements.length).toBe(mockTakeoutSuggestions.length);
  });

  // Error handling
  it('should display error message when API fails', () => {
    llmServiceSpy.getTakeoutRecommendations.and.returnValue(throwError(() => new Error('API Error')));
    component.getTakeoutSuggestions(mockLocation, mockPreferences);
    
    expect(component.errorMessage).toBeTruthy();
    const errorElement = fixture.nativeElement.querySelector('.error-message');
    expect(errorElement).toBeTruthy();
  });

  // UI interactions
  it('should navigate back to form when back button clicked', () => {
    const backButton = fixture.nativeElement.querySelector('.back-button');
    backButton.click();
    expect(router.navigate).toHaveBeenCalledWith(['/meal-form']);
  });

  // Dietary restrictions filtering
  it('should filter suggestions based on dietary restrictions', () => {
    const restrictedPreferences = { ...mockPreferences, dietaryRestrictions: { glutenFree: true } };
    component.getTakeoutSuggestions(mockLocation, restrictedPreferences);
    
    // Verify LLM prompt includes dietary restrictions
    expect(llmService.getTakeoutRecommendations).toHaveBeenCalledWith(
      mockLocation, 
      jasmine.objectContaining({ dietaryRestrictions: jasmine.objectContaining({ glutenFree: true }) })
    );
  });
}